<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.*?>

<StackPane fx:id="backgroundPane" xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.logictrue.controller.MainController">
   <children>
      <!-- 背景图片 -->
      <ImageView fx:id="backgroundImageView" pickOnBounds="false" preserveRatio="false" mouseTransparent="true" />

      <!-- 设置按钮 - 左上角 -->
      <Button fx:id="settingsButton" mnemonicParsing="false" focusTraversable="true" mouseTransparent="false" pickOnBounds="true" StackPane.alignment="TOP_LEFT">
         <StackPane.margin>
            <Insets left="20.0" top="20.0" />
         </StackPane.margin>
      </Button>



      <!-- 全屏按钮 - 左下角 -->
      <Button fx:id="fullscreenButton" mnemonicParsing="false" focusTraversable="true" mouseTransparent="false" pickOnBounds="true" StackPane.alignment="BOTTOM_LEFT">
         <StackPane.margin>
            <Insets bottom="20.0" left="20.0" />
         </StackPane.margin>
      </Button>

      <!-- 退出全屏按钮 - 左下角 -->
      <Button fx:id="exitFullscreenButton" mnemonicParsing="false" focusTraversable="true" mouseTransparent="false" pickOnBounds="true" visible="false" StackPane.alignment="BOTTOM_LEFT">
         <StackPane.margin>
            <Insets bottom="20.0" left="60.0" />
         </StackPane.margin>
      </Button>

      <!-- 页面容器 -->
      <StackPane fx:id="pageContainer" pickOnBounds="false">
         <!-- 主页面 - 使用HBox布局，左侧80%内容，右侧20%快速开始按钮 -->
         <HBox fx:id="mainPage" alignment="CENTER" spacing="0.0">
            <children>
               <!-- 左侧内容区域 - 80%宽度 -->
               <VBox fx:id="mainContentArea" alignment="TOP_CENTER" spacing="20.0" HBox.hgrow="ALWAYS">
                  <children>
                     <!-- 主页标题 -->
                     <Label fx:id="mainTitleLabel" text="欢迎使用IoT数据采集系统" alignment="CENTER" />
                  </children>
                  <HBox.margin>
                     <Insets />
                  </HBox.margin>
               </VBox>

               <!-- 右侧快速开始按钮区域 - 20%宽度 -->
               <VBox fx:id="quickStartArea" alignment="CENTER" prefWidth="200.0" minWidth="200.0" maxWidth="200.0">
                  <children>
                     <Button fx:id="quickStartButton" mnemonicParsing="false" text="快速开始" focusTraversable="true" mouseTransparent="false" pickOnBounds="true" />
                  </children>
                  <HBox.margin>
                     <Insets right="80.0" />
                  </HBox.margin>
               </VBox>
            </children>
         </HBox>

         <!-- 表单页面 -->
         <VBox fx:id="formPage" spacing="20.0" visible="false">
            <children>
               <!-- 按钮行 -->
               <HBox alignment="CENTER">
                  <children>
                     <Button fx:id="backToMainButton" mnemonicParsing="false" text="← 返回" />
                     <Region HBox.hgrow="ALWAYS" />
                     <Button fx:id="dataCollectionButton" mnemonicParsing="false" text="数据采集" />
                     <Button fx:id="dataDetailButton" mnemonicParsing="false" text="数据详情" />
                  </children>
                  <VBox.margin>
                     <Insets bottom="10.0" left="20.0" right="20.0" top="20.0" />
                  </VBox.margin>
               </HBox>

               <!-- 表单标题 -->
               <Label fx:id="formTitleLabel" style="-fx-font-size: 18px; -fx-font-weight: bold;" text="动态数据采集表单">
                  <VBox.margin>
                     <Insets left="20.0" right="20.0" />
                  </VBox.margin>
               </Label>

               <!-- 表单内容滚动面板 -->
               <ScrollPane fx:id="formScrollPane" fitToWidth="true" prefHeight="300.0" VBox.vgrow="ALWAYS">
                  <content>
                     <VBox fx:id="formContainer" spacing="15.0">
                        <padding>
                           <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                        </padding>
                     </VBox>
                  </content>
                  <VBox.margin>
                     <Insets left="20.0" right="20.0" />
                  </VBox.margin>
               </ScrollPane>

               <!-- 状态区域 -->
               <HBox alignment="CENTER_LEFT" spacing="10.0">
                  <children>
                     <ProgressIndicator fx:id="progressIndicator" prefHeight="20.0" prefWidth="20.0" />
                     <Label fx:id="statusLabel" text="" />
                  </children>
                  <VBox.margin>
                     <Insets left="20.0" right="20.0" />
                  </VBox.margin>
               </HBox>

               <!-- 外部应用程序按钮区域 - 表单页面底部 -->
               <HBox fx:id="externalAppsBox" alignment="CENTER" spacing="15.0">
                  <VBox.margin>
                     <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                  </VBox.margin>
               </HBox>
            </children>
         </VBox>

         <!-- 数据详情页面 -->
         <VBox fx:id="dataDetailPage" spacing="20.0" visible="false">
         </VBox>
      </StackPane>
   </children>
</StackPane>
