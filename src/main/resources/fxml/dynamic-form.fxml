<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<VBox spacing="20.0" xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.logictrue.controller.DynamicFormController">
   <children>
      <!-- 表单标题 -->
      <Label fx:id="formTitleLabel" style="-fx-font-size: 18px; -fx-font-weight: bold;" text="动态数据采集表单" />

      <!-- 表单内容滚动面板 -->
      <ScrollPane fx:id="formScrollPane" fitToWidth="true">
         <content>
            <VBox fx:id="formContainer" spacing="15.0">
               <padding>
                  <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
               </padding>
            </VBox>
         </content>
      </ScrollPane>

      <!-- 状态区域 -->
      <HBox alignment="CENTER_LEFT" spacing="10.0">
         <children>
            <ProgressIndicator fx:id="progressIndicator" prefHeight="20.0" prefWidth="20.0" />
            <Label fx:id="statusLabel" text="" />
         </children>
      </HBox>

      <!-- 按钮区域 -->
      <HBox alignment="CENTER_RIGHT" spacing="10.0">
         <children>
            <Button fx:id="submitButton" disable="true" mnemonicParsing="false" text="提交" />
            <Button fx:id="backButton" mnemonicParsing="false" text="返回" />
         </children>
      </HBox>
   </children>
   <padding>
      <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
   </padding>
</VBox>
