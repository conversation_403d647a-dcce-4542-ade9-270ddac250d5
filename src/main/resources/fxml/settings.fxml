<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<VBox spacing="20.0" xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.logictrue.controller.SettingsController">
   <children>
      <!-- 标题 -->
      <Label style="-fx-font-size: 18px; -fx-font-weight: bold;" text="系统设置" />

      <!-- 选项卡面板 -->
      <TabPane fx:id="settingsTabPane" tabClosingPolicy="UNAVAILABLE">
         <tabs>
            <!-- 基本设置选项卡 -->
            <Tab text="基本设置">
               <content>
                  <VBox spacing="15.0">
                     <padding>
                        <Insets bottom="15.0" left="15.0" right="15.0" top="15.0" />
                     </padding>
                     <children>
                        <!-- 设置表单 -->
                        <GridPane hgap="10.0" vgap="15.0">
                          <columnConstraints>
                            <ColumnConstraints hgrow="NEVER" minWidth="120.0" />
                            <ColumnConstraints hgrow="ALWAYS" minWidth="200.0" />
                              <ColumnConstraints hgrow="NEVER" minWidth="100.0" />
                          </columnConstraints>
                          <rowConstraints>
                            <RowConstraints minHeight="30.0" vgrow="SOMETIMES" />
                            <RowConstraints minHeight="30.0" vgrow="SOMETIMES" />
                            <RowConstraints minHeight="30.0" vgrow="SOMETIMES" />
                              <RowConstraints minHeight="30.0" vgrow="SOMETIMES" />
                              <RowConstraints minHeight="30.0" vgrow="SOMETIMES" />
                              <RowConstraints minHeight="30.0" vgrow="SOMETIMES" />
                              <RowConstraints minHeight="30.0" vgrow="SOMETIMES" />
                              <RowConstraints minHeight="30.0" vgrow="SOMETIMES" />
                              <RowConstraints minHeight="30.0" vgrow="SOMETIMES" />
                              <RowConstraints minHeight="30.0" vgrow="SOMETIMES" />
                          </rowConstraints>
                           <children>
                              <!-- 设备编号 -->
                              <Label text="设备编号:" />
                              <TextField fx:id="deviceIdField" promptText="请输入设备编号" GridPane.columnIndex="1" />

                              <!-- 表单名称 -->
                              <Label text="表单名称:" GridPane.rowIndex="1" />
                              <TextField fx:id="formNameField" promptText="请输入表单名称" GridPane.columnIndex="1" GridPane.rowIndex="1" />

                              <!-- 接口地址 -->
                              <Label text="接口地址:" GridPane.rowIndex="2" />
                              <TextField fx:id="apiUrlField" promptText="请输入表单提交接口地址" GridPane.columnIndex="1" GridPane.rowIndex="2" />

                              <!-- 心跳地址 -->
                              <Label text="心跳地址:" GridPane.rowIndex="3" />
                              <TextField fx:id="heartbeatUrlField" promptText="请输入心跳检测地址" GridPane.columnIndex="1" GridPane.rowIndex="3" />
                              <Button fx:id="testHeartbeatButton" disable="true" mnemonicParsing="false" text="测试连接" GridPane.columnIndex="2" GridPane.rowIndex="3" />

                              <!-- 图片地址 -->
                              <Label text="图片地址:" GridPane.rowIndex="4" />
                              <TextField fx:id="imageUrlField" promptText="请输入设备图片下载地址" GridPane.columnIndex="1" GridPane.rowIndex="4" />
                              <Button fx:id="downloadImageButton" mnemonicParsing="false" text="下载图片" GridPane.columnIndex="2" GridPane.rowIndex="4" />
                              <!-- 采集路径 -->
                              <Label text="采集路径:" GridPane.rowIndex="5" />
                              <TextField fx:id="excelCollectionPathField" promptText="请输入Excel文件采集路径" GridPane.columnIndex="1" GridPane.rowIndex="5" />
                              <Button fx:id="browseExcelPathButton" disable="false" mnemonicParsing="false" text="浏览" GridPane.columnIndex="2" GridPane.rowIndex="5" />

                              <!-- 模板下载地址 -->
                              <Label text="模板下载地址:" GridPane.rowIndex="6" />
                              <TextField fx:id="templateDownloadUrlField" promptText="请输入模板下载接口地址" GridPane.columnIndex="1" GridPane.rowIndex="6" />
                              <Button fx:id="downloadTemplateButton" disable="false" mnemonicParsing="false" text="下载模板" GridPane.columnIndex="2" GridPane.rowIndex="6" />

                              <!-- 自动采集设置 -->
                              <Label text="自动采集设置:" GridPane.rowIndex="7" />
                              <HBox spacing="10.0" alignment="CENTER_LEFT" GridPane.columnIndex="1" GridPane.rowIndex="7">
                                 <children>
                                    <CheckBox fx:id="autoExcelCollectionCheckBox" text="启用自动采集" />
                                    <Label text="间隔(分钟):" />
                                    <TextField fx:id="collectionIntervalField" promptText="30" prefWidth="80.0" />
                                 </children>
                              </HBox>
                           </children>
                        </GridPane>
                     </children>
                  </VBox>
               </content>
            </Tab>

            <!-- 表单字段管理选项卡 -->
            <Tab text="表单字段">
               <content>
                  <VBox spacing="15.0">
                     <padding>
                        <Insets bottom="15.0" left="15.0" right="15.0" top="15.0" />
                     </padding>
                     <children>
                        <!-- 字段管理工具栏 -->
                        <HBox spacing="10.0" alignment="CENTER_LEFT">
                           <children>
                              <Label text="表单字段管理:" style="-fx-font-weight: bold;" />
                              <Button fx:id="addFieldButton" text="新增字段" />
                              <Button fx:id="editFieldButton" text="编辑字段" disable="true" />
                              <Button fx:id="deleteFieldButton" text="删除字段" disable="true" />
                              <Button fx:id="moveUpButton" text="上移" disable="true" />
                              <Button fx:id="moveDownButton" text="下移" disable="true" />
                           </children>
                        </HBox>

                        <!-- 字段列表 -->
                        <TableView fx:id="formFieldsTable" prefHeight="300.0">
                           <columns>
                              <TableColumn fx:id="labelColumn" text="字段标签" prefWidth="200.0" />
                              <TableColumn fx:id="nameColumn" text="字段名称" prefWidth="200.0" />
                              <TableColumn fx:id="typeColumn" text="字段类型" prefWidth="150.0" />
                           </columns>
                        </TableView>
                     </children>
                  </VBox>
               </content>
            </Tab>

            <!-- 外部应用程序管理选项卡 -->
            <Tab text="外部应用">
               <content>
                  <VBox spacing="15.0">
                     <padding>
                        <Insets bottom="15.0" left="15.0" right="15.0" top="15.0" />
                     </padding>
                     <children>
                        <!-- 应用程序管理工具栏 -->
                        <HBox spacing="10.0" alignment="CENTER_LEFT">
                           <children>
                              <Label text="外部应用程序管理:" style="-fx-font-weight: bold;" />
                              <Button fx:id="addAppButton" text="新增应用" />
                              <Button fx:id="editAppButton" text="编辑应用" disable="true" />
                              <Button fx:id="deleteAppButton" text="删除应用" disable="true" />
                              <Button fx:id="launchAppButton" text="启动应用" disable="true" />
                           </children>
                        </HBox>

                        <!-- 应用程序列表 -->
                        <TableView fx:id="externalAppsTable" prefHeight="300.0">
                           <columns>
                              <TableColumn fx:id="appNameColumn" text="应用名称" prefWidth="150.0" />
                              <TableColumn fx:id="appPathColumn" text="程序路径" prefWidth="250.0" />
                              <TableColumn fx:id="appDescriptionColumn" text="描述" prefWidth="150.0" />
                           </columns>
                        </TableView>
                     </children>
                  </VBox>
               </content>
            </Tab>

            <!-- 主页标题配置选项卡 -->
            <Tab text="标题配置">
               <content>
                  <VBox spacing="15.0">
                     <padding>
                        <Insets bottom="15.0" left="15.0" right="15.0" top="15.0" />
                     </padding>
                     <children>
                        <!-- 标题配置表单 -->
                        <GridPane hgap="10.0" vgap="15.0">
                          <columnConstraints>
                            <ColumnConstraints hgrow="NEVER" minWidth="120.0" />
                            <ColumnConstraints hgrow="ALWAYS" minWidth="200.0" />
                              <ColumnConstraints hgrow="NEVER" minWidth="100.0" />
                          </columnConstraints>
                          <rowConstraints>
                            <RowConstraints minHeight="30.0" vgrow="SOMETIMES" />
                            <RowConstraints minHeight="30.0" vgrow="SOMETIMES" />
                            <RowConstraints minHeight="30.0" vgrow="SOMETIMES" />
                              <RowConstraints minHeight="30.0" vgrow="SOMETIMES" />
                          </rowConstraints>
                           <children>
                              <!-- 主页标题 -->
                              <Label text="标题文本:" />
                              <TextField fx:id="mainPageTitleField" promptText="请输入主页标题" GridPane.columnIndex="1" />

                              <!-- 标题字体大小 -->
                              <Label text="字体大小:" GridPane.rowIndex="1" />
                              <TextField fx:id="mainPageTitleFontSizeField" promptText="24.0" GridPane.columnIndex="1" GridPane.rowIndex="1" />

                              <!-- 标题颜色 -->
                              <Label text="标题颜色:" GridPane.rowIndex="2" />
                              <TextField fx:id="mainPageTitleColorField" promptText="#333333" GridPane.columnIndex="1" GridPane.rowIndex="2" />

                              <!-- 距离顶部位置 -->
                              <Label text="距离顶部:" GridPane.rowIndex="3" />
                              <TextField fx:id="mainPageTitleTopMarginField" promptText="50.0" GridPane.columnIndex="1" GridPane.rowIndex="3" />
                           </children>
                        </GridPane>

                        <!-- 标题预览区域 -->
                        <VBox spacing="10.0">
                           <children>
                              <HBox spacing="10.0" alignment="CENTER_LEFT">
                                 <children>
                                    <Label text="标题预览:" style="-fx-font-weight: bold;" />
                                    <Button fx:id="applyTitleStyleButton" text="应用到主页" />
                                 </children>
                              </HBox>
                              <VBox fx:id="titlePreviewArea" alignment="TOP_CENTER" prefHeight="100.0" style="-fx-border-color: #cccccc; -fx-border-width: 1px; -fx-background-color: #f9f9f9;">
                                 <children>
                                    <Label fx:id="titlePreviewLabel" text="预览标题" />
                                 </children>
                              </VBox>
                           </children>
                        </VBox>
                     </children>
                  </VBox>
               </content>
            </Tab>

            <!-- 配置文件管理选项卡 -->
            <Tab text="配置管理">
               <content>
                  <VBox spacing="15.0">
                     <padding>
                        <Insets bottom="15.0" left="15.0" right="15.0" top="15.0" />
                     </padding>
                     <children>
                        <!-- 配置文件信息 -->
                        <VBox spacing="10.0">
                           <children>
                              <Label text="配置文件管理:" style="-fx-font-weight: bold; -fx-font-size: 14px;" />
                              <Label fx:id="configPathLabel" text="配置文件路径: " />
                              <Label fx:id="configSizeLabel" text="文件大小: " />
                              <Label fx:id="configModifiedLabel" text="最后修改: " />
                           </children>
                        </VBox>
                        <Separator />
                        <!-- 配置文件操作 -->
                        <VBox spacing="15.0">
                           <children>
                              <!-- 导入和导出 -->
                              <HBox spacing="10.0" alignment="CENTER_LEFT">
                                 <children>
                                    <Label text="导入与导出:" style="-fx-font-weight: bold;" />
                                    <Button fx:id="exportConfigButton" text="导出配置" />
                                    <Button fx:id="importConfigButton" text="导入配置" />
                                 </children>
                              </HBox>

                              <!-- 重置配置 -->
                              <HBox spacing="10.0" alignment="CENTER_LEFT">
                                 <children>
                                    <Label text="重置配置:" style="-fx-font-weight: bold;" />
                                    <Button fx:id="resetConfigButton" text="恢复默认" />
                                 </children>
                              </HBox>
                           </children>
                        </VBox>
                        <Separator />
                     </children>
                  </VBox>
               </content>
            </Tab>
         </tabs>
      </TabPane>

      <!-- 状态区域 -->
      <HBox alignment="CENTER_LEFT" spacing="10.0">
         <children>
            <ProgressIndicator fx:id="progressIndicator" prefHeight="20.0" prefWidth="20.0" />
            <Label fx:id="statusLabel" text="" />
         </children>
      </HBox>

      <!-- 按钮区域 -->
      <HBox alignment="CENTER_RIGHT" spacing="10.0">
         <children>
            <Button fx:id="saveButton" mnemonicParsing="false" text="保存" />
            <Button fx:id="cancelButton" mnemonicParsing="false" text="取消" />
         </children>
      </HBox>
   </children>
   <padding>
      <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
   </padding>
</VBox>
