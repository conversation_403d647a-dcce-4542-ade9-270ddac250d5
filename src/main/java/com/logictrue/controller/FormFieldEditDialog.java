package com.logictrue.controller;

import com.logictrue.model.FormField;
import javafx.collections.FXCollections;
import javafx.geometry.Insets;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.stage.Modality;
import javafx.stage.Stage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.UUID;

/**
 * 表单字段编辑对话框
 */
public class FormFieldEditDialog {
    private static final Logger logger = LoggerFactory.getLogger(FormFieldEditDialog.class);
    
    private Stage dialogStage;
    private FormField formField;
    private boolean confirmed = false;
    
    // UI组件
    private TextField labelField;
    private TextField nameField;
    private ComboBox<FormField.FieldType> typeComboBox;
    private CheckBox requiredCheckBox;
    
    public FormFieldEditDialog(FormField formField) {
        this.formField = formField != null ? formField : createNewFormField();
        initializeDialog();
    }

    public FormFieldEditDialog(<PERSON><PERSON>ield formField, Stage owner) {
        this.formField = formField != null ? formField : createNewFormField();
        initializeDialog();
        if (owner != null) {
            dialogStage.initOwner(owner);
        }
    }
    
    /**
     * 创建新的表单字段
     */
    private FormField createNewFormField() {
        FormField field = new FormField();
        field.setId(UUID.randomUUID().toString());
        field.setType(FormField.FieldType.TEXT);
        return field;
    }
    
    /**
     * 初始化对话框
     */
    private void initializeDialog() {
        dialogStage = new Stage();
        dialogStage.setTitle(formField.getId() == null || formField.getLabel() == null ? "新增字段" : "编辑字段");
        dialogStage.initModality(Modality.WINDOW_MODAL);
        dialogStage.setResizable(false);
        dialogStage.setAlwaysOnTop(true);
        
        VBox root = new VBox(15);
        root.setPadding(new Insets(20));
        
        // 创建表单
        GridPane formGrid = createFormGrid();
        
        // 创建按钮区域
        HBox buttonBox = createButtonBox();
        
        root.getChildren().addAll(
            new Label("字段配置"),
            formGrid,
            buttonBox
        );
        
        Scene scene = new Scene(root, 400, 300);
        dialogStage.setScene(scene);
        
        // 加载现有数据
        loadFormFieldData();
    }
    
    /**
     * 创建表单网格
     */
    private GridPane createFormGrid() {
        GridPane grid = new GridPane();
        grid.setHgap(10);
        grid.setVgap(10);
        grid.setPadding(new Insets(10));

        // 设置列约束，确保标签列有足够的宽度
        javafx.scene.layout.ColumnConstraints labelColumn = new javafx.scene.layout.ColumnConstraints();
        labelColumn.setMinWidth(80);
        labelColumn.setPrefWidth(100);
        labelColumn.setHgrow(javafx.scene.layout.Priority.NEVER);

        javafx.scene.layout.ColumnConstraints fieldColumn = new javafx.scene.layout.ColumnConstraints();
        fieldColumn.setHgrow(javafx.scene.layout.Priority.ALWAYS);

        grid.getColumnConstraints().addAll(labelColumn, fieldColumn);

        int row = 0;

        // 字段标签
        Label fieldLabelLabel = new Label("字段标签:");
        fieldLabelLabel.setMinWidth(80);
        grid.add(fieldLabelLabel, 0, row);
        labelField = new TextField();
        labelField.setPromptText("请输入字段显示标签");
        grid.add(labelField, 1, row++);

        // 字段名称
        Label fieldNameLabel = new Label("字段名称:");
        fieldNameLabel.setMinWidth(80);
        grid.add(fieldNameLabel, 0, row);
        nameField = new TextField();
        nameField.setPromptText("请输入字段名称（用于数据提交）");
        grid.add(nameField, 1, row++);

        // 字段类型
        Label fieldTypeLabel = new Label("字段类型:");
        fieldTypeLabel.setMinWidth(80);
        grid.add(fieldTypeLabel, 0, row);
        typeComboBox = new ComboBox<>(FXCollections.observableArrayList(FormField.FieldType.values()));
        typeComboBox.setCellFactory(listView -> new ListCell<FormField.FieldType>() {
            @Override
            protected void updateItem(FormField.FieldType item, boolean empty) {
                super.updateItem(item, empty);
                setText(empty ? null : item.getDisplayName());
            }
        });
        typeComboBox.setButtonCell(new ListCell<FormField.FieldType>() {
            @Override
            protected void updateItem(FormField.FieldType item, boolean empty) {
                super.updateItem(item, empty);
                setText(empty ? null : item.getDisplayName());
            }
        });
        grid.add(typeComboBox, 1, row++);

        // 是否必填
        Label requiredLabel = new Label("是否必填:");
        requiredLabel.setMinWidth(80);
        grid.add(requiredLabel, 0, row);
        requiredCheckBox = new CheckBox("必填字段");
        requiredCheckBox.setTooltip(new Tooltip("勾选后，启动外部应用时会校验此字段是否已填写"));
        grid.add(requiredCheckBox, 1, row++);

        return grid;
    }
    
    /**
     * 创建按钮区域
     */
    private HBox createButtonBox() {
        HBox buttonBox = new HBox(10);
        buttonBox.setStyle("-fx-alignment: center-right;");
        
        Button confirmButton = new Button("确定");
        confirmButton.setOnAction(event -> {
            if (validateAndSaveFormField()) {
                confirmed = true;
                dialogStage.close();
            }
        });
        
        Button cancelButton = new Button("取消");
        cancelButton.setOnAction(event -> {
            confirmed = false;
            dialogStage.close();
        });
        
        buttonBox.getChildren().addAll(confirmButton, cancelButton);
        return buttonBox;
    }
    
    /**
     * 加载表单字段数据
     */
    private void loadFormFieldData() {
        if (formField != null) {
            labelField.setText(formField.getLabel());
            nameField.setText(formField.getName());
            typeComboBox.setValue(formField.getType());
            requiredCheckBox.setSelected(formField.isRequired());
        }
    }
    
    /**
     * 验证并保存表单字段数据
     */
    private boolean validateAndSaveFormField() {
        // 验证必填字段
        if (labelField.getText().trim().isEmpty()) {
            showAlert("字段标签不能为空");
            return false;
        }
        
        if (nameField.getText().trim().isEmpty()) {
            showAlert("字段名称不能为空");
            return false;
        }
        
        if (typeComboBox.getValue() == null) {
            showAlert("请选择字段类型");
            return false;
        }
        
        // 保存数据
        formField.setLabel(labelField.getText().trim());
        formField.setName(nameField.getText().trim());
        formField.setType(typeComboBox.getValue());
        formField.setRequired(requiredCheckBox.isSelected());

        logger.info("表单字段保存成功: {}", formField);
        return true;
    }
    
    /**
     * 显示警告对话框
     */
    private void showAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.WARNING);
        alert.setTitle("输入验证");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.initOwner(dialogStage);
        alert.initModality(Modality.WINDOW_MODAL);
        alert.showAndWait();
    }
    
    /**
     * 显示对话框并等待结果
     */
    public boolean showAndWait() {
        dialogStage.showAndWait();
        return confirmed;
    }
    
    /**
     * 获取编辑后的表单字段
     */
    public FormField getFormField() {
        return formField;
    }
}
