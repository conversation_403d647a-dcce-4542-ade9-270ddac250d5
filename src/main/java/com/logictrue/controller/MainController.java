package com.logictrue.controller;

import com.logictrue.config.ConfigManager;
import com.logictrue.event.EventBus;
import com.logictrue.model.ExternalApp;
import com.logictrue.model.FormField;
import com.logictrue.service.*;
import com.logictrue.service.FormValidationService;
import com.logictrue.util.FormLayoutUtil;
import javafx.application.Platform;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.StackPane;
import javafx.scene.layout.VBox;
import javafx.scene.shape.SVGPath;
import javafx.stage.Modality;
import javafx.stage.Stage;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.URL;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.ResourceBundle;

/**
 * 主界面控制器
 */
public class MainController implements Initializable {
    private static final Logger logger = LoggerFactory.getLogger(MainController.class);

    @FXML
    private StackPane backgroundPane;

    @FXML
    private ImageView backgroundImageView;

    @FXML
    private Button settingsButton;

    @FXML
    private Button quickStartButton;

    @FXML
    private Button fullscreenButton;

    @FXML
    private Button exitFullscreenButton;

    @FXML
    private Button backToMainButton;

    @FXML
    private Button dataCollectionButton;

    @FXML
    private Button dataDetailButton;

    @FXML
    private HBox mainPage;

    @FXML
    private VBox mainContentArea;

    @FXML
    private Label mainTitleLabel;

    @FXML
    private VBox formPage;

    @FXML
    private VBox dataDetailPage;

    @FXML
    private HBox externalAppsBox;

    // 表单相关控件
    @FXML
    private Label formTitleLabel;

    @FXML
    private ScrollPane formScrollPane;

    @FXML
    private VBox formContainer;

    @FXML
    private Button submitButton;

    @FXML
    private ProgressIndicator progressIndicator;

    @FXML
    private Label statusLabel;

    private ConfigManager configManager;
    private HeartbeatService heartbeatService;
    private ExternalAppService externalAppService;
    private NetworkService networkService;
    private FormValidationService formValidationService;
    private Map<String, Control> fieldControls = new HashMap<>();
    private Stage primaryStage;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        configManager = ConfigManager.getInstance();
        heartbeatService = new HeartbeatService();
        externalAppService = new ExternalAppService();
        networkService = new NetworkService();
        formValidationService = new FormValidationService();

        // 初始化界面
        initializeUI();

        // 初始化按钮状态
        initializeButtonStates();

        // 启动心跳服务
        heartbeatService.start();

        // 加载背景图片
        loadBackgroundImage();

        // 加载外部应用程序按钮
        loadExternalAppButtons();

        // 初始化表单
        initializeForm();

        // 初始化主页标题
        initializeMainPageTitle();

        // 注册背景图片更新事件监听器
        registerBackgroundImageUpdateListener();

        // 设置窗口大小变化监听器
        setupWindowResizeListener();

        logger.info("主界面初始化完成");

        DatabaseService.getInstance().initDatabase();
    }

    /**
     * 初始化主页标题
     */
    private void initializeMainPageTitle() {
        if (mainTitleLabel != null) {
            // 设置标题文本
            mainTitleLabel.setText(configManager.getMainPageTitle());

            // 获取配置参数
            double fontSize = configManager.getMainPageTitleFontSize();
            String titleColor = configManager.getMainPageTitleColor();
            double topMargin = configManager.getMainPageTitleTopMargin();

            // 设置标题样式
            mainTitleLabel.setStyle(String.format(
                "-fx-font-size: %.1fpx; -fx-font-weight: bold; -fx-text-alignment: center; -fx-text-fill: %s;",
                fontSize, titleColor
            ));

            // 设置样式类
            mainTitleLabel.getStyleClass().clear();
            mainTitleLabel.getStyleClass().add("main-page-title");

            // 设置标题的顶部边距
            javafx.scene.layout.VBox.setMargin(mainTitleLabel, new javafx.geometry.Insets(topMargin, 0, 0, 0));

            logger.info("主页标题初始化完成: {}, 字体大小: {}, 颜色: {}, 顶部边距: {}",
                configManager.getMainPageTitle(), fontSize, titleColor, topMargin);
        }
    }

    /**
     * 初始化UI组件
     */
    private void initializeUI() {
        // 确保背景面板不拦截鼠标事件
        backgroundPane.setPickOnBounds(false);

        // 设置背景图片自适应大小
        setupBackgroundImageBinding();

        // 设置快速开始按钮样式
        quickStartButton.getStyleClass().clear();
        quickStartButton.getStyleClass().add("quick-start-button");

        // 设置设置按钮样式和SVG图标
        settingsButton.getStyleClass().clear();
        settingsButton.getStyleClass().add("settings-button");
        setupSettingsButtonIcon();

        // 设置全屏按钮样式和SVG图标
        fullscreenButton.getStyleClass().clear();
        fullscreenButton.getStyleClass().add("fullscreen-button");
        setupFullscreenButtonIcon();

        // 设置退出全屏按钮样式和SVG图标
        exitFullscreenButton.getStyleClass().clear();
        exitFullscreenButton.getStyleClass().add("exit-fullscreen-button");
        setupExitFullscreenButtonIcon();

        // 确保按钮可以接收鼠标事件和焦点
        settingsButton.setMouseTransparent(false);
        quickStartButton.setMouseTransparent(false);
        fullscreenButton.setMouseTransparent(false);
        exitFullscreenButton.setMouseTransparent(false);
        settingsButton.setDisable(false);
        quickStartButton.setDisable(false);
        fullscreenButton.setDisable(false);
        exitFullscreenButton.setDisable(false);
        settingsButton.setFocusTraversable(true);
        quickStartButton.setFocusTraversable(true);
        fullscreenButton.setFocusTraversable(true);
        exitFullscreenButton.setFocusTraversable(true);
        settingsButton.setPickOnBounds(true);
        quickStartButton.setPickOnBounds(true);
        fullscreenButton.setPickOnBounds(true);
        exitFullscreenButton.setPickOnBounds(true);

        // 确保按钮在最上层
        settingsButton.toFront();
        quickStartButton.toFront();
        fullscreenButton.toFront();
        exitFullscreenButton.toFront();

        // 绑定事件
        settingsButton.setOnAction(event -> {
            logger.info("设置按钮Action事件被触发");
            openSettingsWindow();
        });
        quickStartButton.setOnAction(event -> {
            logger.info("快速开始按钮Action事件被触发");
            showFormPage();
        });
        backToMainButton.setOnAction(event -> {
            logger.info("返回主页面按钮Action事件被触发");
            showMainPage();
        });

        dataCollectionButton.setOnAction(event -> {
            logger.info("数据采集按钮Action事件被触发");
            performDataCollection();
        });

        dataDetailButton.setOnAction(event -> {
            logger.info("数据详情按钮Action事件被触发");
            showDataDetailPage();
        });

        fullscreenButton.setOnAction(event -> {
            logger.info("全屏按钮Action事件被触发");
            enterFullscreen();
        });

        exitFullscreenButton.setOnAction(event -> {
            logger.info("退出全屏按钮Action事件被触发");
            exitFullscreen();
        });


    }

    /**
     * 设置设置按钮的SVG图标
     */
    private void setupSettingsButtonIcon() {
        // 创建SVG路径 - 简洁的设置齿轮图标
        SVGPath settingsIcon = new SVGPath();
        settingsIcon.setContent("M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z");
        settingsIcon.setFill(javafx.scene.paint.Color.web("#666666"));

        // 清除按钮文本并设置图标
        settingsButton.setText("");
        settingsButton.setGraphic(settingsIcon);
    }

    /**
     * 设置全屏按钮的SVG图标
     */
    private void setupFullscreenButtonIcon() {
        // 创建SVG路径 - 全屏图标
        SVGPath fullscreenIcon = new SVGPath();
        fullscreenIcon.setContent("M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z");
        fullscreenIcon.setFill(javafx.scene.paint.Color.web("#666666"));

        // 清除按钮文本并设置图标
        fullscreenButton.setText("");
        fullscreenButton.setGraphic(fullscreenIcon);
    }

    /**
     * 设置退出全屏按钮的SVG图标
     */
    private void setupExitFullscreenButtonIcon() {
        // 创建SVG路径 - 退出全屏图标
        SVGPath exitFullscreenIcon = new SVGPath();
        exitFullscreenIcon.setContent("M5 16h3v3h2v-5H5v2zm3-8H5v2h5V5H8v3zm6 11h2v-3h3v-2h-5v5zm2-11V5h-2v5h5V8h-3z");
        exitFullscreenIcon.setFill(javafx.scene.paint.Color.web("#666666"));

        // 清除按钮文本并设置图标
        exitFullscreenButton.setText("");
        exitFullscreenButton.setGraphic(exitFullscreenIcon);
    }

    /**
     * 设置背景图片自适应大小
     */
    private void setupBackgroundImageBinding() {
        // 监听背景面板的大小变化
        backgroundPane.widthProperty().addListener((obs, oldVal, newVal) -> {
            if (backgroundImageView.getImage() != null && newVal.doubleValue() > 0) {
                backgroundImageView.setFitWidth(newVal.doubleValue());
            }
        });

        backgroundPane.heightProperty().addListener((obs, oldVal, newVal) -> {
            if (backgroundImageView.getImage() != null && newVal.doubleValue() > 0) {
                backgroundImageView.setFitHeight(newVal.doubleValue());
            }
        });

        // 初始设置背景图片大小
        backgroundPane.sceneProperty().addListener((obs, oldScene, newScene) -> {
            if (newScene != null) {
                newScene.windowProperty().addListener((obs2, oldWindow, newWindow) -> {
                    if (newWindow != null) {
                        newWindow.showingProperty().addListener((obs3, oldShowing, newShowing) -> {
                            if (newShowing && backgroundImageView.getImage() != null) {
                                // 确保背景图片填满整个面板
                                backgroundImageView.setFitWidth(backgroundPane.getWidth());
                                backgroundImageView.setFitHeight(backgroundPane.getHeight());
                            }
                        });
                    }
                });
            }
        });
    }

    /**
     * 加载背景图片
     * 优先使用配置文件中指定的背景图片，如果路径为空或不存在则使用默认背景
     */
    private void loadBackgroundImage() {
        String backgroundImagePath = configManager.getBackgroundImagePath();

        // 如果背景图片路径为空，直接使用默认背景
        if (backgroundImagePath == null || backgroundImagePath.trim().isEmpty()) {
            logger.info("背景图片路径为空，使用默认背景图片");
            loadDefaultBackground();
            return;
        }

        // 尝试加载配置文件中指定的背景图片
        File imageFile = new File(backgroundImagePath);
        if (imageFile.exists()) {
            try {
                Image backgroundImage = new Image(imageFile.toURI().toString());
                backgroundImageView.setImage(backgroundImage);
                // 设置背景图片大小以适应容器
                updateBackgroundImageSize();
                logger.info("加载配置的背景图片成功: {}", backgroundImagePath);
                return; // 成功加载，直接返回
            } catch (Exception e) {
                logger.error("加载配置的背景图片失败: {}", backgroundImagePath, e);
            }
        } else {
            logger.warn("配置的背景图片文件不存在: {}", backgroundImagePath);
        }

        // 如果配置的背景图片加载失败，尝试加载下载目录中的最新图片
        if (tryLoadLatestDownloadedImage()) {
            return; // 成功加载下载的图片
        }

        // 最后加载默认背景图片
        loadDefaultBackground();
    }

    /**
     * 尝试加载下载目录中的最新图片作为背景
     */
    private boolean tryLoadLatestDownloadedImage() {
        try {
            // 获取图片下载目录
            String jarDir = getJarDirectory();
            String imagesDir = jarDir + File.separator + "images";
            File imagesDirFile = new File(imagesDir);

            if (!imagesDirFile.exists() || !imagesDirFile.isDirectory()) {
                logger.debug("图片下载目录不存在: {}", imagesDir);
                return false;
            }

            // 查找最新的图片文件
            File[] imageFiles = imagesDirFile.listFiles((dir, name) -> {
                String lowerName = name.toLowerCase();
                return lowerName.endsWith(".jpg") || lowerName.endsWith(".jpeg") ||
                       lowerName.endsWith(".png") || lowerName.endsWith(".gif") ||
                       lowerName.endsWith(".bmp") || lowerName.endsWith(".webp");
            });

            if (imageFiles != null && imageFiles.length > 0) {
                // 按修改时间排序，获取最新的图片
                File latestImage = null;
                long latestTime = 0;
                for (File imageFile : imageFiles) {
                    if (imageFile.lastModified() > latestTime) {
                        latestTime = imageFile.lastModified();
                        latestImage = imageFile;
                    }
                }

                if (latestImage != null) {
                    try {
                        Image backgroundImage = new Image(latestImage.toURI().toString());
                        backgroundImageView.setImage(backgroundImage);
                        updateBackgroundImageSize();

                        // 更新配置文件中的背景图片路径
                        configManager.setBackgroundImagePath(latestImage.getAbsolutePath());

                        logger.info("加载最新下载的背景图片成功: {}", latestImage.getAbsolutePath());
                        return true;
                    } catch (Exception e) {
                        logger.error("加载最新下载的图片失败: {}", latestImage.getAbsolutePath(), e);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("尝试加载下载目录中的图片时发生异常", e);
        }
        return false;
    }

    /**
     * 获取jar文件所在目录（与NetworkService中的方法保持一致）
     */
    private String getJarDirectory() {
        try {
            String jarPath = MainController.class.getProtectionDomain()
                    .getCodeSource().getLocation().toURI().getPath();
            File jarFile = new File(jarPath);

            if (jarFile.isFile() && jarPath.endsWith(".jar")) {
                return jarFile.getParent();
            } else {
                return System.getProperty("user.dir");
            }
        } catch (Exception e) {
            logger.warn("获取jar目录失败，使用当前工作目录", e);
            return System.getProperty("user.dir");
        }
    }

    /**
     * 加载默认背景图片
     */
    private void loadDefaultBackground() {
        try {
            // 尝试加载默认背景图片
            URL defaultImageUrl = getClass().getResource("/images/default-background.png");
            if (defaultImageUrl != null) {
                Image defaultImage = new Image(defaultImageUrl.toString());
                backgroundImageView.setImage(defaultImage);
                // 设置背景图片大小以适应容器
                updateBackgroundImageSize();
                logger.info("加载默认背景图片成功");
            } else {
                logger.warn("默认背景图片不存在");
            }
        } catch (Exception e) {
            logger.error("加载默认背景图片失败", e);
        }
    }

    /**
     * 更新背景图片大小以适应容器
     */
    private void updateBackgroundImageSize() {
        if (backgroundImageView.getImage() != null) {
            Platform.runLater(() -> {
                backgroundImageView.setFitWidth(backgroundPane.getWidth());
                backgroundImageView.setFitHeight(backgroundPane.getHeight());
            });
        }
    }

    /**
     * 打开设置窗口
     */
    @FXML
    private void openSettingsWindow() {
        logger.info("开始打开设置窗口");
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/settings.fxml"));
            Parent root = loader.load();

            SettingsController settingsController = loader.getController();
            settingsController.setMainController(this);

            Stage settingsStage = new Stage();
            settingsStage.setTitle("设置");
            settingsStage.setScene(new Scene(root, 700, 600));

            // 设置弹窗模态性和所有者窗口，确保弹窗始终在主界面上方
            settingsStage.initModality(Modality.WINDOW_MODAL);
            settingsStage.initOwner(primaryStage);
            settingsStage.setResizable(true);

            // 设置弹窗始终在最前面
            settingsStage.setAlwaysOnTop(true);

            // 设置弹窗图标（如果主窗口有图标的话）
            if (primaryStage != null && !primaryStage.getIcons().isEmpty()) {
                settingsStage.getIcons().addAll(primaryStage.getIcons());
            }

            settingsStage.showAndWait();
        } catch (IOException e) {
            logger.error("打开设置窗口失败", e);
        }
    }

    /**
     * 显示表单页面
     */
    private void showFormPage() {
        logger.info("切换到表单页面");
        mainPage.setVisible(false);
        formPage.setVisible(true);

        // 隐藏设置按钮和快速开始按钮
        settingsButton.setVisible(false);
        quickStartButton.setVisible(false);

        // 重新生成表单内容
        generateDynamicForm();
    }

    /**
     * 更新主页标题显示
     */
    public void updateMainPageTitle() {
        initializeMainPageTitle();
    }

    /**
     * 从配置文件重新加载标题样式（用于恢复保存的样式）
     */
    public void reloadTitleStyleFromConfig() {
        // 重新加载配置文件
        configManager = ConfigManager.getInstance();
        initializeMainPageTitle();
    }

    /**
     * 显示主页面
     */
    public void showMainPage() {
        logger.info("切换到主页面");
        formPage.setVisible(false);
        dataDetailPage.setVisible(false);
        mainPage.setVisible(true);

        // 显示设置按钮和快速开始按钮
        settingsButton.setVisible(true);
        quickStartButton.setVisible(true);

        // 清理表单状态
        clearFormStatus();
    }

    /**
     * 显示数据详情页面
     */
    private void showDataDetailPage() {
        logger.info("切换到数据详情页面");
        try {
            // 加载数据详情页面
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/data-list.fxml"));
            Parent dataDetailContent = loader.load();

            // 获取控制器并设置主控制器引用
            DataListController dataListController = loader.getController();
            dataListController.setMainController(this);

            // 清空数据详情页面并添加新内容
            dataDetailPage.getChildren().clear();
            dataDetailPage.getChildren().add(dataDetailContent);

            // 切换页面显示
            mainPage.setVisible(false);
            formPage.setVisible(false);
            dataDetailPage.setVisible(true);

            // 隐藏设置按钮和快速开始按钮
            settingsButton.setVisible(false);
            quickStartButton.setVisible(false);

        } catch (IOException e) {
            logger.error("加载数据详情页面失败", e);
            showAlert("错误", "无法加载数据详情页面: " + e.getMessage());
        }
    }

    /**
     * 初始化按钮状态
     */
    private void initializeButtonStates() {
        // 两个按钮同时显示
        fullscreenButton.setVisible(true);
        exitFullscreenButton.setVisible(true);
    }

    /**
     * 更新背景图片
     */
    public void updateBackgroundImage(String imagePath) {
        Platform.runLater(() -> {
            if (imagePath != null && !imagePath.isEmpty()) {
                File imageFile = new File(imagePath);
                if (imageFile.exists()) {
                    try {
                        Image newImage = new Image(imageFile.toURI().toString());
                        backgroundImageView.setImage(newImage);
                        // 设置背景图片大小以适应容器
                        updateBackgroundImageSize();
                        logger.info("背景图片更新成功: {}", imagePath);
                    } catch (Exception e) {
                        logger.error("更新背景图片失败: {}", imagePath, e);
                    }
                }
            }
        });
    }

    /**
     * 刷新背景图片
     * 重新从配置文件加载背景图片，支持外部调用
     */
    public void refreshBackgroundImage() {
        Platform.runLater(() -> {
            logger.info("刷新背景图片");
            loadBackgroundImage();
        });
    }

    /**
     * 检查并更新背景图片
     * 如果有新下载的图片，自动更新背景
     */
    public void checkAndUpdateBackgroundImage() {
        Platform.runLater(() -> {
            // 重新加载配置
            configManager = ConfigManager.getInstance();
            loadBackgroundImage();
        });
    }

    /**
     * 注册背景图片更新事件监听器
     */
    private void registerBackgroundImageUpdateListener() {
        EventBus.getInstance().registerBackgroundImageUpdateListener(event -> {
            logger.info("收到背景图片更新事件: {}", event.getImagePath());
            // 在JavaFX应用线程中更新背景图片
            Platform.runLater(() -> {
                updateBackgroundImage(event.getImagePath());
            });
        });
        logger.info("已注册背景图片更新事件监听器");
    }

    /**
     * 设置窗口大小变化监听器
     */
    private void setupWindowResizeListener() {
        // 延迟执行，确保Scene已经设置
        Platform.runLater(() -> {
            try {
                if (formScrollPane != null && formScrollPane.getScene() != null) {
                    Stage stage = (Stage) formScrollPane.getScene().getWindow();
                    if (stage != null) {
                        // 监听窗口高度变化
                        stage.heightProperty().addListener((observable, oldValue, newValue) -> {
                            // 当窗口大小变化时，重新调整表单高度
                            if (Math.abs(newValue.doubleValue() - oldValue.doubleValue()) > 50) {
                                Platform.runLater(() -> refreshFormLayout());
                            }
                        });
                        logger.info("已设置窗口大小变化监听器");
                    }
                }
            } catch (Exception e) {
                logger.warn("设置窗口大小变化监听器失败", e);
            }
        });
    }

    /**
     * 刷新表单布局
     */
    private void refreshFormLayout() {
        try {
            List<FormField> formFields = configManager.getFormFields();
            if (!formFields.isEmpty()) {
                // 重新计算布局信息
                FormLayoutUtil.FormLayoutInfo layoutInfo =
                    FormLayoutUtil.calculateFormLayout(formFields, 2);
                FormLayoutUtil.adjustMainFormHeight(formScrollPane, layoutInfo);
                logger.debug("刷新表单布局完成");
            }
        } catch (Exception e) {
            logger.warn("刷新表单布局失败", e);
        }
    }

    /**
     * 收集当前表单数据
     */
    private Map<String, Object> collectFormData() {
        Map<String, Object> formData = new HashMap<>();

        for (Map.Entry<String, Control> entry : fieldControls.entrySet()) {
            String fieldName = entry.getKey();
            Control control = entry.getValue();
            Object value = getControlValue(control);

            if (value != null) {
                formData.put(fieldName, value);
            }

            logger.debug("收集表单数据: {} = {}", fieldName, value);
        }

        logger.info("收集表单数据完成，共{}个字段", formData.size());
        return formData;
    }

    /**
     * 获取控件的值
     */
    private Object getControlValue(Control control) {
        if (control instanceof TextField) {
            return ((TextField) control).getText();
        } else if (control instanceof TextArea) {
            return ((TextArea) control).getText();
        } else if (control instanceof DatePicker) {
            return ((DatePicker) control).getValue();
        } else if (control instanceof ComboBox) {
            return ((ComboBox<?>) control).getValue();
        } else if (control instanceof CheckBox) {
            return ((CheckBox) control).isSelected();
        }
        return null;
    }

    /**
     * 显示必填字段校验失败对话框
     */
    private void showRequiredFieldsValidationDialog(FormValidationService.ValidationResult validationResult, ExternalApp app) {
        Alert alert = new Alert(Alert.AlertType.WARNING);
        alert.setTitle("必填字段校验");
        alert.setHeaderText("启动 " + app.getName() + " 前需要填写必填字段");
        alert.setContentText(validationResult.getErrorMessage());

        // 添加按钮
        ButtonType fillFieldsButton = new ButtonType("去填写", ButtonBar.ButtonData.OK_DONE);

        ButtonType cancelButton = new ButtonType("取消", ButtonBar.ButtonData.CANCEL_CLOSE);

        alert.getButtonTypes().setAll(fillFieldsButton, cancelButton);

        alert.showAndWait().ifPresent(response -> {
            if (response == fillFieldsButton) {
                // 切换到表单页面
                showFormPage();
            } /*else if (response == forceStartButton) {
                // 强制启动应用
                logger.warn("用户选择强制启动应用: {}", app.getName());
                performLaunchExternalApp(app);
            }*/
            // 取消则什么都不做
        });
    }

    /**
     * 显示错误对话框
     */
    private void showErrorDialog(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }


    /**
     * 获取心跳服务
     */
    public HeartbeatService getHeartbeatService() {
        return heartbeatService;
    }

    /**
     * 加载外部应用程序按钮
     */
    private void loadExternalAppButtons() {
        List<ExternalApp> externalApps = configManager.getExternalApps();
        externalAppsBox.getChildren().clear();

        for (ExternalApp app : externalApps) {
            Button appButton = new Button(app.getName());
            appButton.getStyleClass().add("external-app-button");
            appButton.setOnAction(event -> launchExternalApp(app));
            externalAppsBox.getChildren().add(appButton);
        }

        logger.info("加载外部应用程序按钮完成，共{}个应用", externalApps.size());
    }

    /**
     * 启动外部应用程序
     */
    private void launchExternalApp(ExternalApp app) {
        logger.info("启动外部应用程序: {}", app.getName());

        // 检查是否有必填字段需要校验
        if (formValidationService.hasRequiredFields()) {
            // 收集当前表单数据
            Map<String, Object> formData = collectFormData();

            // 校验必填字段
            FormValidationService.ValidationResult validationResult =
                formValidationService.validateRequiredFields(formData);

            if (!validationResult.isValid()) {
                // 显示校验失败对话框
                showRequiredFieldsValidationDialog(validationResult, app);
                return;
            }
        }

        // 校验通过或无必填字段，启动应用
        performLaunchExternalApp(app);
    }

    /**
     * 执行启动外部应用程序
     */
    private void performLaunchExternalApp(ExternalApp app) {
        externalAppService.launchApp(app).thenAccept(success -> {
            Platform.runLater(() -> {
                if (success) {
                    logger.info("外部应用程序启动成功: {}", app.getName());
                } else {
                    logger.error("外部应用程序启动失败: {}", app.getName());
                    showErrorDialog("启动失败", "外部应用程序启动失败: " + app.getName());
                }
            });
        });
    }

    /**
     * 刷新外部应用程序按钮（在设置更新后调用）
     */
    public void refreshExternalAppButtons() {
        Platform.runLater(this::loadExternalAppButtons);
    }

    /**
     * 初始化表单
     */
    private void initializeForm() {
        // 隐藏进度指示器
        progressIndicator.setVisible(false);

        // 绑定表单事件
        submitButton.setOnAction(event -> submitForm());

        // 设置表单容器
        formContainer.setSpacing(15);
        formContainer.setPadding(new Insets(20));

        // 设置表单标题
        formTitleLabel.setText(configManager.getFormName());
    }

    /**
     * 生成动态表单
     */
    private void generateDynamicForm() {
        List<FormField> formFields = configManager.getFormFields();
        fieldControls.clear();

        GridPane formGrid = new GridPane();
        formGrid.setHgap(15);
        formGrid.setVgap(15);

        int row = 0;
        int col = 0;
        final int maxColumns = 2; // 每行最多2个字段
        int textAreaCount = 0; // 统计文本区域数量

        for (FormField field : formFields) {
            // 创建标签
            Label label = new Label(field.getLabel() + ":");
            label.setMinWidth(100);

            // 创建输入控件
            Control inputControl = createInputControl(field);
            fieldControls.put(field.getName(), inputControl);

            // 添加到网格
            formGrid.add(label, col * 2, row);
            formGrid.add(inputControl, col * 2 + 1, row);

            // 更新位置
            col++;
            if (col >= maxColumns || field.getType() == FormField.FieldType.TEXTAREA) {
                col = 0;
                row++;

                // 如果是文本区域，占满整行并统计数量
                if (field.getType() == FormField.FieldType.TEXTAREA) {
                    GridPane.setColumnSpan(label, 1);
                    GridPane.setColumnSpan(inputControl, maxColumns * 2 - 1);
                    textAreaCount++;
                }
            }
        }

        formContainer.getChildren().clear();
        formContainer.getChildren().add(formGrid);

        // 根据字段数量动态调整表单高度
        FormLayoutUtil.FormLayoutInfo layoutInfo = new FormLayoutUtil.FormLayoutInfo(
            formFields.size(), textAreaCount, row);
        FormLayoutUtil.adjustMainFormHeight(formScrollPane, layoutInfo);

        // 更新提交按钮状态
        updateSubmitButtonState();
    }

    /**
     * 创建输入控件
     */
    private Control createInputControl(FormField field) {
        Control control;

        switch (field.getType()) {
            case TEXTAREA:
                TextArea textArea = new TextArea();
                textArea.setPromptText("请输入" + field.getLabel());
                textArea.setPrefRowCount(3);
                textArea.setWrapText(true);
                control = textArea;
                break;

            case NUMBER:
                TextField numberField = new TextField();
                numberField.setPromptText("请输入" + field.getLabel());
                // 添加数字验证
                numberField.textProperty().addListener((observable, oldValue, newValue) -> {
                    if (!newValue.matches("\\d*\\.?\\d*")) {
                        numberField.setText(oldValue);
                    }
                });
                control = numberField;
                break;

            case DATE:
                DatePicker datePicker = new DatePicker();
                datePicker.setPromptText("请选择" + field.getLabel());
                control = datePicker;
                break;

            case TEXT:
            default:
                TextField textField = new TextField();
                textField.setPromptText("请输入" + field.getLabel());
                control = textField;
                break;
        }

        return control;
    }

    /**
     * 更新提交按钮状态
     */
    private void updateSubmitButtonState() {
        // 简化逻辑，只要有字段就可以提交
        submitButton.setDisable(false);
    }

    /**
     * 提交表单
     */
    private void submitForm() {
        // 验证表单数据
        if (!validateForm()) {
            return;
        }

        // 收集表单数据
        Map<String, Object> formData = collectFormData();

        // 显示进度指示器
        progressIndicator.setVisible(true);
        submitButton.setDisable(true);
        showStatus("正在提交表单...", true);

        Task<Boolean> submitTask = new Task<Boolean>() {
            @Override
            protected Boolean call() throws Exception {
                return networkService.submitForm(formData).get();
            }
        };

        submitTask.setOnSucceeded(event -> {
            Platform.runLater(() -> {
                progressIndicator.setVisible(false);
                updateSubmitButtonState();

                boolean success = submitTask.getValue();
                if (success) {
                    showStatus("表单提交成功", true);
                    logger.info("动态表单提交成功: {}", formData);

                    // 延迟返回主页面
                    Platform.runLater(() -> {
                        try {
                            Thread.sleep(1500);
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                        }
                        showMainPage();
                    });
                } else {
                    showStatus("表单提交失败", false);
                }
            });
        });

        submitTask.setOnFailed(event -> {
            Platform.runLater(() -> {
                progressIndicator.setVisible(false);
                updateSubmitButtonState();
                showStatus("表单提交异常: " + submitTask.getException().getMessage(), false);
            });
        });

        Thread submitThread = new Thread(submitTask);
        submitThread.setDaemon(true);
        submitThread.start();
    }

    /**
     * 验证表单数据
     */
    private boolean validateForm() {
        // 简化验证，总是返回true
        return true;
    }

    /**
     * 显示状态信息
     */
    private void showStatus(String message, boolean success) {
        statusLabel.setText(message);
        statusLabel.getStyleClass().clear();
        statusLabel.getStyleClass().add(success ? "status-success" : "status-error");
    }

    /**
     * 清理表单状态
     */
    private void clearFormStatus() {
        progressIndicator.setVisible(false);
        statusLabel.setText("");
        updateSubmitButtonState();
    }

    /**
     * 设置主舞台
     */
    public void setPrimaryStage(Stage stage) {
        this.primaryStage = stage;
    }

    /**
     * 进入全屏模式
     */
    private void enterFullscreen() {
        if (primaryStage != null) {
            // 保存当前窗口状态
            boolean wasMaximized = primaryStage.isMaximized();

            // 设置全屏并隐藏系统边框
            primaryStage.setFullScreen(true);

            // 切换按钮显示 - 两个按钮同时显示
            fullscreenButton.setVisible(true);
            exitFullscreenButton.setVisible(true);

            // 更新背景图片大小以适应全屏
            updateBackgroundImageSize();

            logger.info("进入全屏模式");
        }
    }

    /**
     * 退出全屏模式
     */
    private void exitFullscreen() {
        if (primaryStage != null) {
            // 退出全屏
            primaryStage.setFullScreen(false);

            // 切换按钮显示 - 两个按钮同时显示
            fullscreenButton.setVisible(true);
            exitFullscreenButton.setVisible(true);

            // 更新背景图片大小以适应窗口
            updateBackgroundImageSize();

            logger.info("退出全屏模式");
        }
    }

    /**
     * 应用关闭时的清理工作
     */
    public void shutdown() {
        if (heartbeatService != null) {
            heartbeatService.stop();
        }
        logger.info("主界面控制器关闭");
    }

    /**
     * 执行数据采集
     */
    private void performDataCollection() {
        try {
            logger.info("开始执行数据采集");

            // 检查Excel采集路径配置
            String excelCollectionPath = configManager.getExcelCollectionPath();
            if (excelCollectionPath == null || excelCollectionPath.trim().isEmpty()) {
                showAlert("配置错误", "请先在设置中配置Excel文件采集路径");
                return;
            }

            // 检查模板下载地址配置
            String templateDownloadUrl = configManager.getTemplateDownloadUrl();
            if (templateDownloadUrl == null || templateDownloadUrl.trim().isEmpty()) {
                showAlert("配置错误", "请先在设置中配置模板下载地址");
                return;
            }

            String deviceId = configManager.getConfig().getDeviceId();

            // 显示进度指示器
            progressIndicator.setVisible(true);
            dataCollectionButton.setDisable(true);
            showStatus("正在执行数据采集...", false);

            // 创建数据采集任务
            Task<Boolean> collectionTask = new Task<Boolean>() {
                @Override
                protected Boolean call() throws Exception {
                    return performExcelDataCollection(deviceId, excelCollectionPath, templateDownloadUrl);
                }
            };

            collectionTask.setOnSucceeded(event -> {
                Platform.runLater(() -> {
                    progressIndicator.setVisible(false);
                    dataCollectionButton.setDisable(false);

                    boolean success = collectionTask.getValue();
                    if (success) {
                        showStatus("数据采集完成", true);
                        logger.info("数据采集成功完成");
                    } else {
                        showStatus("数据采集失败", false);
                        logger.warn("数据采集失败");
                    }
                });
            });

            collectionTask.setOnFailed(event -> {
                Platform.runLater(() -> {
                    progressIndicator.setVisible(false);
                    dataCollectionButton.setDisable(false);

                    Throwable exception = collectionTask.getException();
                    String errorMessage = exception != null ? exception.getMessage() : "未知错误";
                    showStatus("数据采集异常: " + errorMessage, false);
                    logger.error("数据采集异常", exception);

                    showAlert("数据采集失败", "数据采集过程中发生异常：" + errorMessage);
                });
            });

            // 在后台线程中执行任务
            Thread collectionThread = new Thread(collectionTask);
            collectionThread.setDaemon(true);
            collectionThread.start();

        } catch (Exception e) {
            logger.error("启动数据采集失败", e);
            showAlert("启动失败", "启动数据采集失败：" + e.getMessage());

            // 恢复UI状态
            progressIndicator.setVisible(false);
            dataCollectionButton.setDisable(false);
        }
    }

    /**
     * 执行Excel数据采集的具体逻辑
     */
    private boolean performExcelDataCollection(String deviceId, String excelCollectionPath, String templateDownloadUrl) {
        try {
            logger.info("开始执行Excel数据采集，路径: {}", excelCollectionPath);

            // 1. 扫描Excel文件
            List<File> excelFiles = scanExcelFiles(excelCollectionPath);
            if (excelFiles.isEmpty()) {
                logger.warn("未找到Excel文件，路径: {}", excelCollectionPath);
                return true; // 没有文件不算失败
            }

            logger.info("找到Excel文件数量: {}", excelFiles.size());

            // 2. 初始化服务
            com.logictrue.service.TemplateService templateService = new com.logictrue.service.TemplateService();
            com.logictrue.service.ExcelParsingService excelParsingService = new com.logictrue.service.ExcelParsingService();
            com.logictrue.service.DatabaseService databaseService = com.logictrue.service.DatabaseService.getInstance();

            // 3. 检查本地模板文件，如果没有则尝试下载
            String templateFilePath = null;

            if (templateService.hasLocalTemplateFiles()) {
                // 使用本地已有的最新模板文件
                templateFilePath = templateService.getLatestLocalTemplateFile();
                logger.info("使用本地模板文件: {}", templateFilePath);
            } else {
                // 本地没有模板文件，尝试下载
                logger.info("本地未找到模板文件，尝试从配置的地址下载模板");

                if (templateDownloadUrl == null || templateDownloadUrl.trim().isEmpty()) {
                    logger.error("模板下载地址未配置，无法下载模板");
                    return false;
                }

                logger.info("开始下载模板，下载地址: {}", templateDownloadUrl);
                templateFilePath = templateService.downloadTemplateFromUrl(templateDownloadUrl).get();

                if (templateFilePath == null) {
                    logger.error("模板下载失败");
                    return false;
                }

                logger.info("模板下载成功: {}", templateFilePath);
            }

            // 4. 处理每个Excel文件
            int successCount = 0;
            int totalCount = excelFiles.size();

            for (File excelFile : excelFiles) {
                try {
                    logger.info("开始处理Excel文件: {}", excelFile.getName());


                    // 解析Excel文件（使用已确定的模板文件）
                    ExcelParsingService.ExcelParsingResult parsingResult =
                            excelParsingService.parseExcelFile(deviceId, excelFile.getAbsolutePath(), templateFilePath);

                    if (!parsingResult.isSuccess()) {
                        logger.error("解析Excel文件失败: {}, 错误: {}", excelFile.getName(), parsingResult.getErrorMessage());
                    }else {
                        successCount++;
                        logger.info("Excel文件处理成功: {}", excelFile.getName());
                    }

                    // 保存到数据库
                    /*boolean saveSuccess = databaseService.saveExcelParsingResult(parsingResult);
                    if (saveSuccess) {
                        //解析成功后更新记录状态
                        successCount++;
                        logger.info("Excel文件处理成功: {}", excelFile.getName());
                    } else {
                        logger.error("保存Excel解析结果失败: {}", excelFile.getName());
                    }*/

                } catch (Exception e) {
                    logger.error("处理Excel文件异常: {}", excelFile.getName(), e);
                }
            }

            logger.info("Excel数据采集完成，总文件数: {}, 成功处理: {}", totalCount, successCount);
            return successCount > 0; // 至少成功处理一个文件就算成功

        } catch (Exception e) {
            logger.error("Excel数据采集异常", e);
            return false;
        }
    }

    /**
     * 扫描指定路径下的Excel文件
     */
    private List<File> scanExcelFiles(String dirPath) {
        List<File> excelFiles = new ArrayList<>();

        try {
            File dir = new File(dirPath);
            if (!dir.exists() || !dir.isDirectory()) {
                logger.warn("Excel采集路径不存在或不是目录: {}", dirPath);
                return excelFiles;
            }

            File[] files = dir.listFiles((file, name) -> {
                String lowerName = name.toLowerCase();
                return lowerName.endsWith(".xlsx") || lowerName.endsWith(".xls");
            });

            if (files != null) {
                for (File file : files) {
                    if (file.isFile()) {
                        excelFiles.add(file);
                    }
                }
            }

        } catch (Exception e) {
            logger.error("扫描Excel文件失败: {}", dirPath, e);
        }

        return excelFiles;
    }

    /**
     * 显示警告对话框
     */
    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
